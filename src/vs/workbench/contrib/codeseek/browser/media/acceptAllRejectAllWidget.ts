import { Widget } from '../../../../../base/browser/ui/widget.js';
import { ICodeEditor, IOverlayWidget } from '../../../../../editor/browser/editorBrowser.js';
import { Event } from '../../../../../base/common/event.js';
import { URI } from '../../../../../base/common/uri.js';

export class AcceptAllRejectAllWidget extends Widget implements IOverlayWidget {
	private readonly _domNode: HTMLElement;
	private readonly editor: ICodeEditor;
	private readonly ID: string;
	private diffCounter: HTMLElement;
	private mainContainer: HTMLElement;
	private diffActionButtonContainer: HTMLElement;
	private nextFileActionButtonContainer: HTMLElement;
	private hasNextFile: () => boolean;

	constructor({ editor, onAcceptAll, onRejectAll, onNextDiff, onNextFile, hasNextFile, onDidChangeDiffCount }: {
		editor: ICodeEditor;
		onAcceptAll: () => void;
		onRejectAll: () => void;
		onNextDiff: (step: number) => void;
		onNextFile: () => void;
		hasNextFile: () => boolean;
		onDidChangeDiffCount?: Event<{ uri: URI; currentIndex: number; totalDiffs: number }>;
	}) {
		super();
		//todo , 只要修改这里next按钮就可以出来了,样式有问题，需要充分测试，这个功能先关闭
		this.hasNextFile = hasNextFile || (() => false);
		this.ID = `acceptallrejectall-${Date.now()}`;
		this.editor = editor;

		// 创建主容器
		this.mainContainer = document.createElement('div');
		this.mainContainer.className = 'buttonsContainer';

		// 设置容器样式
		const mainContainerStyle = {
			display: 'flex',
			flexDirection: 'row',
			position: 'absolute',
			gap: '8px',
			zIndex: '2',
		};

		// Style the container
		Object.assign(this.mainContainer.style, mainContainerStyle);

		// Create container div with buttons
		this.diffActionButtonContainer = document.createElement('div');
		this.diffActionButtonContainer.className = 'buttons';

		const diffActionButtonContainerStyle = {
			height: '28px',
			zIndex: '2',
			padding: '1px 8px',
			display: 'flex',
			borderRadius: '6px',
			border: '1px solid var(--vscode-commandCenter-inactiveBorder)',
			alignItems: 'center',
			backgroundColor: 'var(--vscode-editorWidget-background)',
		}

		// Style the buttons
		Object.assign(this.diffActionButtonContainer.style, diffActionButtonContainerStyle);

		// Create navigation buttons
		const upButton = document.createElement('div');
		upButton.className = 'navButton';
		const upIcon = document.createElement('span');
		upIcon.className = 'codicon codicon-chevron-up';
		upButton.appendChild(upIcon);

		// Create diff counter display
		this.diffCounter = document.createElement('div');
		this.diffCounter.className = 'diffCounter';
		this.diffCounter.textContent = '0 of 0';

		const downButton = document.createElement('div');
		downButton.className = 'navButton';
		const downIcon = document.createElement('span');
		downIcon.className = 'codicon codicon-chevron-down';
		downButton.appendChild(downIcon);

		// Create container for accept and reject buttons
		const actionButtonsContainer = document.createElement('div');
		actionButtonsContainer.className = 'actionButtons';

		// Create reject and accept buttons
		const rejectButton = document.createElement('div');
		rejectButton.className = 'rejectButton';
		const rejectButtonText = document.createElement('span');
		rejectButtonText.textContent = 'Reject All';
		rejectButton.appendChild(rejectButtonText);

		const rejectShortcut = document.createElement('span');
		rejectShortcut.className = 'shortcutLabel';
		rejectShortcut.textContent = 'Alt+Shift+Backspace';
		rejectButton.appendChild(rejectShortcut);

		const acceptButton = document.createElement('div');
		acceptButton.className = 'acceptButton';
		const acceptButtonText = document.createElement('span');
		acceptButtonText.textContent = 'Accept All';
		acceptButton.appendChild(acceptButtonText);

		const acceptShortcut = document.createElement('span');
		acceptShortcut.className = 'shortcutLabel';
		acceptShortcut.textContent = 'Alt+Shift+Enter';
		acceptButton.appendChild(acceptShortcut);

		// Add buttons to action container
		actionButtonsContainer.appendChild(rejectButton);
		actionButtonsContainer.appendChild(acceptButton);

		// Append navigation elements and action container to main container
		this.diffActionButtonContainer.appendChild(upButton);
		this.diffActionButtonContainer.appendChild(this.diffCounter);
		this.diffActionButtonContainer.appendChild(downButton);
		this.diffActionButtonContainer.appendChild(actionButtonsContainer);

		// 创建Next file容器
		this.nextFileActionButtonContainer = document.createElement('div');
		this.nextFileActionButtonContainer.className = 'nextFileButtons';

		const nextFileButtonContainerStyle = {
			height: '28px',
			width: '88px',
			zIndex: '2',
			padding: '1px 8px',
			display: 'flex',
			borderRadius: '6px',
			border: '1px solid var(--vscode-commandCenter-inactiveBorder)',
			alignItems: 'center',
			backgroundColor: 'var(--vscode-editorWidget-background)',
			justifyContent: 'center',
			cursor: 'pointer',
		}

		// Style the next file buttons
		Object.assign(this.nextFileActionButtonContainer.style, nextFileButtonContainerStyle);

		const nextFileButton = document.createElement('div');
		nextFileButton.className = 'nextFileButton';
		const nextFileButtonStyle = {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			transition: 'all 0.2s ease',
		}
		Object.assign(nextFileButton.style, nextFileButtonStyle);

		const nextFileText = document.createElement('span');
		nextFileText.textContent = 'Next file';
		nextFileButton.appendChild(nextFileText);
		const nextFileTextStyle = {
			fontSize: '10px',
			color: 'var(--vscode-foreground)',
		}
		Object.assign(nextFileText.style, nextFileTextStyle);

		const rightIcon = document.createElement('span');
		rightIcon.className = 'codicon codicon-chevron-right';
		nextFileButton.appendChild(rightIcon);
		const rightIconStyle = {
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			scale: '0.75',
		}
		Object.assign(rightIcon.style, rightIconStyle);
		this.nextFileActionButtonContainer.appendChild(nextFileButton);

		// 将两个按钮组添加到主容器
		this.mainContainer.appendChild(this.diffActionButtonContainer);
		this.mainContainer.appendChild(this.nextFileActionButtonContainer);

		// Style for action buttons container
		const acceptRejectContainerStyle = {
			display: 'flex',
			marginLeft: '12px',
			gap: '10px',
		}

		// Apply style to action buttons container
		Object.assign(actionButtonsContainer.style, acceptRejectContainerStyle);

		const commonButtonStyle = {
			fontSize: '10px',
			padding: '1px 6px',
			cursor: 'pointer',
			borderRadius: '3px',
			transition: 'all 0.2s ease',
		}

		const navButtonStyle = {
			cursor: 'pointer',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			width: '20px',
			height: '20px',
			borderRadius: '3px',
			scale: '0.75',
			transition: 'all 0.2s ease',
			color: 'var(--vscode-foreground)',
		}

		const rejectShortcutLabelStyle = {
			fontSize: '9px',
			opacity: '0.7',
			marginLeft: '4px',
			color: 'var(--vscode-descriptionForeground)',
		};

		const acceptShortcutLabelStyle = {
			fontSize: '9px',
			opacity: '0.7',
			marginLeft: '4px',
			color: 'white',
		};

		// Style for the diff counter
		const diffCounterStyle = {
			fontSize: '10px',
			color: 'var(--vscode-foreground)',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			minWidth: '40px',
			textAlign: 'center',
		}

		const acceptButtonStyle = {
			...commonButtonStyle,
			color: 'var(--vscode-button-foreground)',
			backgroundColor: 'color-mix(in srgb,var(--vscode-button-background) 90%,transparent)',
			display: 'flex',
			alignItems: 'center',
			gap: '2px'
		}

		const rejectButtonStyle = {
			...commonButtonStyle,
			color: 'var(--vscode-foreground)',
			backgroundColor: 'transparent',
			display: 'flex',
			alignItems: 'center',
			gap: '2px'
		}

		// Style diff counter
		Object.assign(this.diffCounter.style, diffCounterStyle);

		// Style navigation buttons
		upButton.addEventListener('click', () => {
			onNextDiff(-1);
		});
		Object.assign(upButton.style, navButtonStyle);
		upButton.addEventListener('mouseenter', () => {
			upButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		upButton.addEventListener('mouseleave', () => {
			upButton.style.backgroundColor = 'transparent';
		});

		downButton.addEventListener('click', () => {
			onNextDiff(1);
		});
		Object.assign(downButton.style, navButtonStyle);
		downButton.addEventListener('mouseenter', () => {
			downButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		downButton.addEventListener('mouseleave', () => {
			downButton.style.backgroundColor = 'transparent';
		});

		// Style accept button
		acceptButton.addEventListener('click', onAcceptAll);
		Object.assign(acceptButton.style, acceptButtonStyle);
		acceptButton.addEventListener('mouseenter', () => {
			acceptButton.style.backgroundColor = 'color-mix(in srgb,var(--vscode-button-background) 50%,transparent)';
		});
		acceptButton.addEventListener('mouseleave', () => {
			acceptButton.style.backgroundColor = 'color-mix(in srgb,var(--vscode-button-background) 90%,transparent)';
		});

		// Style reject button
		rejectButton.addEventListener('click', onRejectAll);
		Object.assign(rejectButton.style, rejectButtonStyle);
		rejectButton.addEventListener('mouseenter', () => {
			rejectButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		rejectButton.addEventListener('mouseleave', () => {
			rejectButton.style.backgroundColor = 'transparent';
		});

		Object.assign(rejectShortcut.style, rejectShortcutLabelStyle);
		Object.assign(acceptShortcut.style, acceptShortcutLabelStyle);

		nextFileButton.addEventListener('mouseenter', () => {
			nextFileButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		nextFileButton.addEventListener('mouseleave', () => {
			nextFileButton.style.backgroundColor = 'transparent';
		});
		if (onNextFile) {
			nextFileButton.addEventListener('click', onNextFile);
		}

		this.updateVisibility(true, hasNextFile);

		if (onDidChangeDiffCount) {
			this._register(onDidChangeDiffCount(({ uri, currentIndex, totalDiffs }) => {
				// 检查当前编辑器的URI是否与事件中的URI匹配
				const editorUri = editor.getModel()?.uri;
				if (editorUri && uri.toString() === editorUri.toString()) {
					this.updateDiffCounter(currentIndex, totalDiffs);
				}
			}));
		}

		this._domNode = this.mainContainer;

		const updateTop = () => {
			const layoutInfo = editor.getLayoutInfo();
			const minMargin = 50;
			const maxMargin = 80;
			const margin = Math.min(Math.max(layoutInfo.height * 0.08, minMargin), maxMargin);
			this._domNode.style.top = `${layoutInfo.height - margin}px`;
		};
		const updateLeft = () => {
			const layoutInfo = editor.getLayoutInfo();
			const minimapWidth = layoutInfo.minimap.minimapWidth;
			const verticalScrollbarWidth = layoutInfo.verticalScrollbarWidth;
			const buttonWidth = this._domNode.offsetWidth;

			const leftPx = layoutInfo.width - minimapWidth - verticalScrollbarWidth - buttonWidth;
			this._domNode.style.left = `${leftPx / 2}px`;
		};

		// Mount the widget
		editor.addOverlayWidget(this);

		updateTop();
		updateLeft();
		this._register(editor.onDidScrollChange(e => { updateTop(); }));
		this._register(editor.onDidChangeModelContent(e => { updateTop(); }));
		this._register(editor.onDidLayoutChange(e => { updateTop(); updateLeft(); }));
	}

	/**
	 * 更新按钮组的可见性
	 * @param hasDiffs 当前文件是否有diff区域
	 * @param hasNextFile 是否有下一个包含diff的文件
	 */
	public updateVisibility(hasDiffs: boolean, hasNextFile: () => boolean): void {
		this.diffActionButtonContainer.style.display = hasDiffs ? 'flex' : 'none';
		this.nextFileActionButtonContainer.style.display = hasNextFile() ? 'flex' : 'none';
	}

	public updateDiffCounter(currentIndex: number, totalDiffs: number): void {
		this.diffCounter.textContent = totalDiffs > 0 ? `${currentIndex + 1} of ${totalDiffs}` : '0 of 0';
		this.updateVisibility(totalDiffs > 0, this.hasNextFile);
	}

	public getId(): string {
		return this.ID;
	}

	public getDomNode(): HTMLElement {
		return this._domNode;
	}

	public getPosition() {
		return null;
	}

	public override dispose(): void {
		this.editor.removeOverlayWidget(this);
		super.dispose();
	}
}
