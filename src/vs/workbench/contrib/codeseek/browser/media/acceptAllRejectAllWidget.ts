import { Widget } from '../../../../../base/browser/ui/widget.js';
import { ICodeEditor, IOverlayWidget } from '../../../../../editor/browser/editorBrowser.js';
import { Event } from '../../../../../base/common/event.js';
import { URI } from '../../../../../base/common/uri.js';

export class AcceptAllRejectAllWidget extends Widget implements IOverlayWidget {
	private readonly _domNode: HTMLElement;
	private readonly editor: ICodeEditor;
	private readonly ID: string;
	private diffCounter: HTMLElement;
	private mainContainer: HTMLElement;
	private diffActionButtonContainer: HTMLElement;
	private nextFileActionButtonContainer: HTMLElement;
	private hasNextFile: () => boolean;

	constructor({ editor, onAcceptAll, onRejectAll, onNextDiff, onNextFile, onPrevFile, hasNextFile, hasPrevFile, getFileNavigationInfo, hasCurrentFileDiffs, onDidChangeDiffCount }: {
		editor: ICodeEditor;
		onAcceptAll: () => void;
		onRejectAll: () => void;
		onNextDiff: (step: number) => void;
		onNextFile: () => void;
		onPrevFile: () => void;
		hasNextFile: () => boolean;
		hasPrevFile: () => boolean;
		getFileNavigationInfo: () => { currentIndex: number; totalFiles: number };
		hasCurrentFileDiffs: () => boolean;
		onDidChangeDiffCount?: Event<{ uri: URI; currentIndex: number; totalDiffs: number }>;
	}) {
		super();
		//todo , 只要修改这里next按钮就可以出来了,样式有问题，需要充分测试，这个功能先关闭
		this.hasNextFile = hasNextFile || (() => false);
		this.ID = `acceptallrejectall-${Date.now()}`;
		this.editor = editor;

		// 创建主容器
		this.mainContainer = document.createElement('div');
		this.mainContainer.className = 'buttonsContainer';

		// 设置容器样式
		const mainContainerStyle = {
			display: 'flex',
			flexDirection: 'row',
			position: 'absolute',
			gap: '8px',
			zIndex: '2',
		};

		// Style the container
		Object.assign(this.mainContainer.style, mainContainerStyle);

		// Create container div with buttons
		this.diffActionButtonContainer = document.createElement('div');
		this.diffActionButtonContainer.className = 'buttons';

		const diffActionButtonContainerStyle = {
			height: '28px',
			zIndex: '2',
			padding: '1px 8px',
			display: 'flex',
			borderRadius: '6px',
			border: '1px solid var(--vscode-commandCenter-inactiveBorder)',
			alignItems: 'center',
			backgroundColor: 'var(--vscode-editorWidget-background)',
		}

		// Style the buttons
		Object.assign(this.diffActionButtonContainer.style, diffActionButtonContainerStyle);

		// Create navigation buttons
		const upButton = document.createElement('div');
		upButton.className = 'navButton';
		const upIcon = document.createElement('span');
		upIcon.className = 'codicon codicon-chevron-up';
		upButton.appendChild(upIcon);

		// Create diff counter display
		this.diffCounter = document.createElement('div');
		this.diffCounter.className = 'diffCounter';
		this.diffCounter.textContent = '0 of 0';

		const downButton = document.createElement('div');
		downButton.className = 'navButton';
		const downIcon = document.createElement('span');
		downIcon.className = 'codicon codicon-chevron-down';
		downButton.appendChild(downIcon);

		// Create container for accept and reject buttons
		const actionButtonsContainer = document.createElement('div');
		actionButtonsContainer.className = 'actionButtons';

		// Create reject and accept buttons
		const rejectButton = document.createElement('div');
		rejectButton.className = 'rejectButton';
		const rejectButtonText = document.createElement('span');
		rejectButtonText.textContent = 'Reject All';
		rejectButton.appendChild(rejectButtonText);

		const rejectShortcut = document.createElement('span');
		rejectShortcut.className = 'shortcutLabel';
		rejectShortcut.textContent = 'Alt+Shift+Backspace';
		rejectButton.appendChild(rejectShortcut);

		const acceptButton = document.createElement('div');
		acceptButton.className = 'acceptButton';
		const acceptButtonText = document.createElement('span');
		acceptButtonText.textContent = 'Accept All';
		acceptButton.appendChild(acceptButtonText);

		const acceptShortcut = document.createElement('span');
		acceptShortcut.className = 'shortcutLabel';
		acceptShortcut.textContent = 'Alt+Shift+Enter';
		acceptButton.appendChild(acceptShortcut);

		// Add buttons to action container
		actionButtonsContainer.appendChild(rejectButton);
		actionButtonsContainer.appendChild(acceptButton);

		// Append navigation elements and action container to main container
		this.diffActionButtonContainer.appendChild(upButton);
		this.diffActionButtonContainer.appendChild(this.diffCounter);
		this.diffActionButtonContainer.appendChild(downButton);
		this.diffActionButtonContainer.appendChild(actionButtonsContainer);

		// 创建文件导航容器
		this.nextFileActionButtonContainer = document.createElement('div');
		this.nextFileActionButtonContainer.className = 'fileNavigationButtons';

		const fileNavButtonContainerStyle = {
			height: '28px',
			zIndex: '2',
			padding: '1px 8px',
			display: 'flex',
			borderRadius: '6px',
			border: '1px solid var(--vscode-commandCenter-inactiveBorder)',
			alignItems: 'center',
			backgroundColor: 'var(--vscode-editorWidget-background)',
			gap: '4px',
		}

		// Style the file navigation container
		Object.assign(this.nextFileActionButtonContainer.style, fileNavButtonContainerStyle);

		// 创建上一个文件按钮
		const prevFileButton = document.createElement('div');
		prevFileButton.className = 'prevFileButton';
		const prevIcon = document.createElement('span');
		prevIcon.className = 'codicon codicon-chevron-left';
		prevFileButton.appendChild(prevIcon);

		// 创建文件计数显示
		const fileCounter = document.createElement('div');
		fileCounter.className = 'fileCounter';
		fileCounter.textContent = '1/1';

		// 创建下一个文件按钮
		const nextFileButton = document.createElement('div');
		nextFileButton.className = 'nextFileButton';
		const nextIcon = document.createElement('span');
		nextIcon.className = 'codicon codicon-chevron-right';
		nextFileButton.appendChild(nextIcon);

		// 创建"Review next file"按钮（当前文件无diff时显示）
		const reviewNextFileButton = document.createElement('div');
		reviewNextFileButton.className = 'reviewNextFileButton';
		reviewNextFileButton.style.display = 'none'; // 默认隐藏

		const reviewNextFileText = document.createElement('span');
		reviewNextFileText.textContent = 'Review next file';
		reviewNextFileButton.appendChild(reviewNextFileText);

		const reviewNextIcon = document.createElement('span');
		reviewNextIcon.className = 'codicon codicon-chevron-right';
		reviewNextFileButton.appendChild(reviewNextIcon);

		// 添加所有元素到容器
		this.nextFileActionButtonContainer.appendChild(prevFileButton);
		this.nextFileActionButtonContainer.appendChild(fileCounter);
		this.nextFileActionButtonContainer.appendChild(nextFileButton);
		this.nextFileActionButtonContainer.appendChild(reviewNextFileButton);

		// 将两个按钮组添加到主容器
		this.mainContainer.appendChild(this.diffActionButtonContainer);
		this.mainContainer.appendChild(this.nextFileActionButtonContainer);

		// Style for action buttons container
		const acceptRejectContainerStyle = {
			display: 'flex',
			marginLeft: '12px',
			gap: '10px',
		}

		// Apply style to action buttons container
		Object.assign(actionButtonsContainer.style, acceptRejectContainerStyle);

		const commonButtonStyle = {
			fontSize: '10px',
			padding: '1px 6px',
			cursor: 'pointer',
			borderRadius: '3px',
			transition: 'all 0.2s ease',
		}

		const navButtonStyle = {
			cursor: 'pointer',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			width: '20px',
			height: '20px',
			borderRadius: '3px',
			scale: '0.75',
			transition: 'all 0.2s ease',
			color: 'var(--vscode-foreground)',
		}

		const rejectShortcutLabelStyle = {
			fontSize: '9px',
			opacity: '0.7',
			marginLeft: '4px',
			color: 'var(--vscode-descriptionForeground)',
		};

		const acceptShortcutLabelStyle = {
			fontSize: '9px',
			opacity: '0.7',
			marginLeft: '4px',
			color: 'white',
		};

		// Style for the diff counter
		const diffCounterStyle = {
			fontSize: '10px',
			color: 'var(--vscode-foreground)',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			minWidth: '40px',
			textAlign: 'center',
		}

		const acceptButtonStyle = {
			...commonButtonStyle,
			color: 'var(--vscode-button-foreground)',
			backgroundColor: 'color-mix(in srgb,var(--vscode-button-background) 90%,transparent)',
			display: 'flex',
			alignItems: 'center',
			gap: '2px'
		}

		const rejectButtonStyle = {
			...commonButtonStyle,
			color: 'var(--vscode-foreground)',
			backgroundColor: 'transparent',
			display: 'flex',
			alignItems: 'center',
			gap: '2px'
		}

		// Style diff counter
		Object.assign(this.diffCounter.style, diffCounterStyle);

		// Style navigation buttons
		upButton.addEventListener('click', () => {
			onNextDiff(-1);
		});
		Object.assign(upButton.style, navButtonStyle);
		upButton.addEventListener('mouseenter', () => {
			upButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		upButton.addEventListener('mouseleave', () => {
			upButton.style.backgroundColor = 'transparent';
		});

		downButton.addEventListener('click', () => {
			onNextDiff(1);
		});
		Object.assign(downButton.style, navButtonStyle);
		downButton.addEventListener('mouseenter', () => {
			downButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		downButton.addEventListener('mouseleave', () => {
			downButton.style.backgroundColor = 'transparent';
		});

		// Style accept button
		acceptButton.addEventListener('click', onAcceptAll);
		Object.assign(acceptButton.style, acceptButtonStyle);
		acceptButton.addEventListener('mouseenter', () => {
			acceptButton.style.backgroundColor = 'color-mix(in srgb,var(--vscode-button-background) 50%,transparent)';
		});
		acceptButton.addEventListener('mouseleave', () => {
			acceptButton.style.backgroundColor = 'color-mix(in srgb,var(--vscode-button-background) 90%,transparent)';
		});

		// Style reject button
		rejectButton.addEventListener('click', onRejectAll);
		Object.assign(rejectButton.style, rejectButtonStyle);
		rejectButton.addEventListener('mouseenter', () => {
			rejectButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		rejectButton.addEventListener('mouseleave', () => {
			rejectButton.style.backgroundColor = 'transparent';
		});

		Object.assign(rejectShortcut.style, rejectShortcutLabelStyle);
		Object.assign(acceptShortcut.style, acceptShortcutLabelStyle);

		// 文件导航按钮样式
		const fileNavButtonStyle = {
			cursor: 'pointer',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			width: '20px',
			height: '20px',
			borderRadius: '3px',
			scale: '0.75',
			transition: 'all 0.2s ease',
			color: 'var(--vscode-foreground)',
		};

		const fileCounterStyle = {
			fontSize: '10px',
			color: 'var(--vscode-foreground)',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			minWidth: '30px',
			textAlign: 'center',
		};

		const reviewNextFileButtonStyle = {
			cursor: 'pointer',
			display: 'flex',
			alignItems: 'center',
			justifyContent: 'center',
			padding: '2px 8px',
			borderRadius: '3px',
			transition: 'all 0.2s ease',
			color: 'var(--vscode-foreground)',
			fontSize: '10px',
			gap: '4px',
		};

		// 应用样式
		Object.assign(prevFileButton.style, fileNavButtonStyle);
		Object.assign(nextFileButton.style, fileNavButtonStyle);
		Object.assign(fileCounter.style, fileCounterStyle);
		Object.assign(reviewNextFileButton.style, reviewNextFileButtonStyle);

		// 添加事件处理
		prevFileButton.addEventListener('mouseenter', () => {
			prevFileButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		prevFileButton.addEventListener('mouseleave', () => {
			prevFileButton.style.backgroundColor = 'transparent';
		});

		nextFileButton.addEventListener('mouseenter', () => {
			nextFileButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		nextFileButton.addEventListener('mouseleave', () => {
			nextFileButton.style.backgroundColor = 'transparent';
		});

		// Review next file 按钮事件
		reviewNextFileButton.addEventListener('mouseenter', () => {
			reviewNextFileButton.style.backgroundColor = 'var(--vscode-editor-background)';
		});
		reviewNextFileButton.addEventListener('mouseleave', () => {
			reviewNextFileButton.style.backgroundColor = 'transparent';
		});

		// 绑定点击事件
		if (onPrevFile) {
			prevFileButton.addEventListener('click', () => {
				const info = getFileNavigationInfo();
				if (info.totalFiles > 1) {
					onPrevFile();
				}
			});
		}
		if (onNextFile) {
			nextFileButton.addEventListener('click', () => {
				const info = getFileNavigationInfo();
				if (info.totalFiles > 1) {
					onNextFile();
				}
			});
		}

		// Review next file 按钮点击事件
		if (onNextFile) {
			reviewNextFileButton.addEventListener('click', () => {
				const info = getFileNavigationInfo();
				if (info.totalFiles > 0) {
					onNextFile();
				}
			});
		}

		// 更新文件计数显示和按钮状态
		const updateFileCounter = (hasDiffs: boolean = true) => {
			const info = getFileNavigationInfo();
			fileCounter.textContent = `${info.currentIndex + 1}/${info.totalFiles}`;

			// 只要有多个文件，按钮就始终可用（支持循环切换）
			const hasMultipleFiles = info.totalFiles > 1;

			if (hasDiffs) {
				// 当前文件有diff：显示导航按钮，隐藏Review next file按钮
				prevFileButton.style.display = 'flex';
				fileCounter.style.display = 'flex';
				nextFileButton.style.display = 'flex';
				reviewNextFileButton.style.display = 'none';

				prevFileButton.style.opacity = hasMultipleFiles ? '1' : '0.5';
				prevFileButton.style.cursor = hasMultipleFiles ? 'pointer' : 'default';

				nextFileButton.style.opacity = hasMultipleFiles ? '1' : '0.5';
				nextFileButton.style.cursor = hasMultipleFiles ? 'pointer' : 'default';
			} else {
				// 当前文件无diff：隐藏导航按钮，显示Review next file按钮
				prevFileButton.style.display = 'none';
				fileCounter.style.display = 'none';
				nextFileButton.style.display = 'none';
				reviewNextFileButton.style.display = info.totalFiles > 0 ? 'flex' : 'none';
			}
		};

		// 初始化文件计数 - 检查当前文件是否真的有diff
		const initialHasDiffs = hasCurrentFileDiffs();
		updateFileCounter(initialHasDiffs);

		this.updateVisibility(initialHasDiffs, hasNextFile, updateFileCounter);

		if (onDidChangeDiffCount) {
			this._register(onDidChangeDiffCount(({ uri, currentIndex, totalDiffs }) => {
				// 检查当前编辑器的URI是否与事件中的URI匹配
				const editorUri = editor.getModel()?.uri;
				if (editorUri && uri.toString() === editorUri.toString()) {
					this.updateDiffCounter(currentIndex, totalDiffs);
					updateFileCounter(totalDiffs > 0); // 同时更新文件计数，传递是否有diff
				}
			}));
		}

		// 监听编辑器模型变化（文件切换）
		this._register(editor.onDidChangeModel(() => {
			// 当文件切换时，需要重新检查当前文件是否有diff
			setTimeout(() => {
				const currentUri = editor.getModel()?.uri;
				if (currentUri) {
					// 使用准确的方法检查当前文件是否有diff
					const hasDiffs = hasCurrentFileDiffs();
					updateFileCounter(hasDiffs);
					this.updateVisibility(hasDiffs, hasNextFile, updateFileCounter);
				}
			}, 100); // 短暂延迟确保diff状态已更新
		}));

		this._domNode = this.mainContainer;

		const updateTop = () => {
			const layoutInfo = editor.getLayoutInfo();
			const minMargin = 50;
			const maxMargin = 80;
			const margin = Math.min(Math.max(layoutInfo.height * 0.08, minMargin), maxMargin);
			this._domNode.style.top = `${layoutInfo.height - margin}px`;
		};
		const updateLeft = () => {
			const layoutInfo = editor.getLayoutInfo();
			const minimapWidth = layoutInfo.minimap.minimapWidth;
			const verticalScrollbarWidth = layoutInfo.verticalScrollbarWidth;
			const buttonWidth = this._domNode.offsetWidth;

			const leftPx = layoutInfo.width - minimapWidth - verticalScrollbarWidth - buttonWidth;
			this._domNode.style.left = `${leftPx / 2}px`;
		};

		// Mount the widget
		editor.addOverlayWidget(this);

		updateTop();
		updateLeft();
		this._register(editor.onDidScrollChange(() => { updateTop(); }));
		this._register(editor.onDidChangeModelContent(() => { updateTop(); }));
		this._register(editor.onDidLayoutChange(() => { updateTop(); updateLeft(); }));
	}

	/**
	 * 更新按钮组的可见性
	 * @param hasDiffs 当前文件是否有diff区域
	 * @param hasNextFile 是否有下一个包含diff的文件
	 * @param updateFileCounter 更新文件计数的回调函数
	 */
	public updateVisibility(hasDiffs: boolean, hasNextFile: () => boolean, updateFileCounter?: (hasDiffs: boolean) => void): void {
		this.diffActionButtonContainer.style.display = hasDiffs ? 'flex' : 'none';

		// 显示文件导航控件当有文件时（无论当前文件是否有diff）
		if (updateFileCounter) {
			updateFileCounter(hasDiffs);
			// 从文件计数中获取总文件数来决定是否显示导航控件
			const fileCounterText = (this.nextFileActionButtonContainer.querySelector('.fileCounter') as HTMLElement)?.textContent || '0/0';
			const totalFiles = parseInt(fileCounterText.split('/')[1] || '0');
			// 只要有文件就显示导航控件（可能显示Review next file按钮）
			this.nextFileActionButtonContainer.style.display = totalFiles > 0 || !hasDiffs ? 'flex' : 'none';
		} else {
			// 如果没有updateFileCounter，使用原来的逻辑
			this.nextFileActionButtonContainer.style.display = hasNextFile() ? 'flex' : 'none';
		}
	}

	public updateDiffCounter(currentIndex: number, totalDiffs: number): void {
		this.diffCounter.textContent = totalDiffs > 0 ? `${currentIndex + 1} of ${totalDiffs}` : '0 of 0';
		this.updateVisibility(totalDiffs > 0, this.hasNextFile);
	}

	public getId(): string {
		return this.ID;
	}

	public getDomNode(): HTMLElement {
		return this._domNode;
	}

	public getPosition() {
		return null;
	}

	public override dispose(): void {
		this.editor.removeOverlayWidget(this);
		super.dispose();
	}
}
